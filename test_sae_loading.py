#!/usr/bin/env python3
"""
Test script to verify SAE loading works with the new models.
"""

import sys
import os
sys.path.append('itas')

def test_sae_detection():
    """Test SAE detection methods."""

    print("Testing SAE detection methods...")

    try:
        from itas.core.sae import SAE
        print("✅ Successfully imported SAE")

        # Test detection methods
        print("\nTesting detection methods...")

        # Test fnlp detection
        repo_id1 = "fnlp/Llama3_1-8B-Base-LXR-32x"
        source_type1 = SAE._detect_source_type(repo_id1)
        print(f"fnlp repo detected as: {source_type1}")

        # Test google detection
        repo_id2 = "google/gemma-scope-9b-pt-res"
        source_type2 = SAE._detect_source_type(repo_id2)
        print(f"google repo detected as: {source_type2}")

        print("✅ Detection methods work!")

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_sae_detection()
